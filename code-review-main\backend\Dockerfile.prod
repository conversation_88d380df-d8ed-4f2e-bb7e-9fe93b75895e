# Common build stage
FROM node:18-alpine as common-build-stage

COPY . ./app

WORKDIR /app

# RUN npm i -g pm2 --registry=https://registry.npm.taobao.org
RUN npm i -g pm2
RUN yarn install && yarn build

# Production build stage
FROM common-build-stage as production-build-stage

ENV NODE_ENV production

EXPOSE 3000

# CMD ["yarn", "deploy:prod", "&&", "sleep", "infinity"]
# 启动前延迟 5 秒，防止出现mysql没有初始化完成导致的失败
CMD ["sh", "-c", "sleep 5 && yarn deploy:prod"]