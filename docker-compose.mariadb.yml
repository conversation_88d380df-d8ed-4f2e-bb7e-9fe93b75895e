services:
  frontend:
    build:
      context: ./frontend  # 指向前端项目代码所在目录
      dockerfile: Dockerfile.prod    # Dockerfile 的名称
      args:
        VITE_APP_APIHOST: http://${IP}:${API_PORT}/v1
    ports:
      - "${CONSOLE_PORT}:80"
    restart: always
    depends_on:
      - backend
    networks:
      - backend-network

  backend:
    build:
      context: ./backend  # 指向后端项目代码所在目录
      dockerfile: Dockerfile.prod   # Dockerfile 的名称
    environment:
      - PORT=${API_PORT}
      - NODE_ENV=${NODE_ENV}
      - DOMAIN=${IP}:${API_PORT}
      - DB_TYPE=${DB_TYPE}
      - DB_HOST=${DB_HOST}
      - DB_PORT=${DB_PORT}
      - DB_USER=${DB_USER}
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_DATABASE=${DB_DATABASE}
      - CONSOLE_PORT=${CONSOLE_PORT}
      - IP=${IP}
    ports:
      - "${API_PORT}:9000"
    restart: always
    depends_on:
      - mariadb
    networks:
      - backend-network

  mariadb:
    image: uhub.service.ucloud.cn/code-review/centos-mariadb:10.5
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD}
      MYSQL_DATABASE: ${DB_DATABASE}
      MYSQL_USER: mysql
      MYSQL_PASSWORD: ${DB_PASSWORD}
      # 默认用户使用兼容插件
      MYSQL_AUTHENTICATION_PLUGIN: mysql_native_password
    volumes:
      - ./mysql/my.cnf:/etc/mysql/conf.d/my.cnf:ro
      - ./mysql/data:/var/lib/mysql
      - ./mysql/init:/docker-entrypoint-initdb.d
    ports:
      - "${DB_PORT}:3306"
    restart: always
    networks:
      - backend-network

networks:
  backend-network:
    driver: bridge