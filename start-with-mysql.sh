#!/bin/bash

# 启动脚本 - 使用 MySQL 数据库
# 使用方法: ./start-with-mysql.sh

echo "正在启动 Code Review 系统 (MySQL 版本)..."

# 检查 .env 文件是否存在
if [ ! -f ".env" ]; then
    echo "错误: .env 文件不存在，请先创建 .env 文件"
    exit 1
fi

# 加载环境变量
source .env

echo "配置信息:"
echo "  IP: $IP"
echo "  前端端口: $CONSOLE_PORT"
echo "  API端口: $API_PORT"
echo "  数据库端口: $DB_PORT"
echo "  API地址: http://$IP:$API_PORT/v1"

# 创建必要的目录
mkdir -p mysql/data

# 启动服务
echo "正在启动 Docker 容器..."
docker-compose -f docker-compose.mysql.yml up -d

echo "启动完成！"
echo ""
echo "访问地址:"
echo "  前端: http://$IP:$CONSOLE_PORT"
echo "  API: http://$IP:$API_PORT"
echo ""
echo "查看日志:"
echo "  docker-compose -f docker-compose.mysql.yml logs -f"
echo ""
echo "停止服务:"
echo "  docker-compose -f docker-compose.mysql.yml down"
