server {
    listen 80;
    server_name localhost;  # 替换为你的域名或 IP 地址
    root /usr/share/nginx/html;  # 指定静态文件的根目录

    index index.html;

    underscores_in_headers on;

    location / {
        try_files $uri $uri/ /index.html;
    }

    error_page 404 /index.html;  # 当出现 404 错误时，返回 index.html

    # 可选：增加对压缩的支持，提升性能
    gzip on;
    gzip_types text/plain application/javascript application/x-javascript text/javascript text/xml text/css image/svg+xml image/x-icon;
    gzip_vary on;
    gzip_min_length 1024;
}