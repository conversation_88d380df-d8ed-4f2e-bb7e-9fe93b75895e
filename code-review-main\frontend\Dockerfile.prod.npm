# 备用 Dockerfile - 使用 npm 而不是 pnpm
# 如果 pnpm 构建失败，可以使用这个版本

# Common build stage
FROM node:18-alpine as common-build-stage

COPY . ./app

WORKDIR /app

# 安装依赖并构建
RUN npm install --legacy-peer-deps
RUN npm run build

# Production stage
FROM nginx:1.19.1 as production-build-stage

COPY ./nginx.conf /etc/nginx/conf.d/default.conf

COPY --from=common-build-stage /app/dist /usr/share/nginx/html

# 创建配置目录
RUN mkdir -p /usr/share/nginx/html/config

# 复制启动脚本
COPY docker-entrypoint.sh /docker-entrypoint.sh
RUN chmod +x /docker-entrypoint.sh

EXPOSE 80

CMD ["/docker-entrypoint.sh"]
