.setting {
  padding: 0 10px;

  .setting-title {
    font-size: 14px;
    padding-bottom: 10px;
    font-weight: 600;
  }

  .setting-radio {
    display: flex;
    padding-bottom: 10px;

    .setting-radio-item {
      position: relative;
      width: 44px;
      height: 36px;
      margin-right: 16px;
      overflow: hidden;
      background-color: #f0f2f5;
      border-radius: 4px;
      box-shadow: 0 1px 2.5px 0 rgba(0, 0, 0, 0.18);
      cursor: pointer;

      &:before {
        position: absolute;
        top: 0;
        left: 0;
        width: 33%;
        height: 100%;
        background-color: #fff;
        content: '';
      }

      &:after {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 25%;
        background-color: #fff;
        content: '';
      }

      &.style-light {
        &:before {
          background-color: #fff;
        }

        &:after {
          background-color: #fff;
        }
      }

      &.style-dark {
        &:before {
          z-index: 1;
          background-color: #001529;
        }

        &:after {
          background-color: #fff;
        }
      }

      &.style-all-dark {
        background-color: #00000057;

        &:before {
          z-index: 1;
          background-color: #001529;
        }

        &:after {
          background-color: #001529;
        }
      }

      &.nav-inline {
        &:before {
          z-index: 1;
          background-color: #001529;
        }

        &:after {
          background-color: #fff;
        }
      }

      &.nav-horizontal {
        &:before {
          background-color: #f0f2f5;
        }

        &:after {
          background-color: #001529;
        }
      }

      .choose-icon {
        position: absolute;
        right: 6px;
        bottom: 4px;
        color: #1890ff;
        font-weight: 700;
        font-size: 14px;
        pointer-events: none;
      }
    }
  }

  .setting-list {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    font-size: 14px;
    font-variant: tabular-nums;
    position: relative;

    .setting-list-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 10px 0;

      .setting-list-item-action {
        flex: 0 0 auto;
        margin-left: 40px;
        padding: 0;
      }
    }
  }
}
