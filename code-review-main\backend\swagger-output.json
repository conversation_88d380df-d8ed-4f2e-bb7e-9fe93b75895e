{"openapi": "3.0.0", "info": {"title": "api document", "version": "1.0.0", "description": "swagger guide：https://swagger.io/docs/specification/basic-structure/"}, "servers": [{"url": "http://localhost:9000", "description": "local environment"}], "paths": {"openapi": {"0": "3", "1": ".", "2": "0", "3": ".", "4": "0"}, "info": {"title": "ACL-Pro", "version": "1.0"}, "/v1/ai-manager": {"get": {"tags": ["AI Manager"], "summary": "获取 AI 管理列表", "parameters": [{"$ref": "#/components/parameters/JWTToken"}, {"$ref": "#/components/parameters/Current"}, {"$ref": "#/components/parameters/PageSize"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AiManagerListResponse"}}}}, "500": {"description": "Server Error"}}}, "post": {"tags": ["AI Manager"], "summary": "创建 AI 管理", "parameters": [{"$ref": "#/components/parameters/JWTToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AiManagerRequestBody"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AiManagerResponse"}}}}, "500": {"description": "Server Error"}}}, "put": {"tags": ["AI Manager"], "summary": "更新 AI 管理", "parameters": [{"$ref": "#/components/parameters/JWTToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AiManagerPutRequestBody"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AiManagerResponse"}}}}, "500": {"description": "Server Error"}}}, "delete": {"tags": ["AI Manager"], "summary": "删除 AI 管理", "parameters": [{"$ref": "#/components/parameters/JWTToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["id"], "properties": {"id": {"description": "待删除的 AI 管理 ID", "type": "number"}}}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AiManagerResponse"}}}}, "500": {"description": "Server Error"}}}}, "/v1/ai/message": {"get": {"tags": ["AI Manager"], "summary": "获取AI生成的评论列表", "parameters": [{"$ref": "#/components/parameters/JWTToken"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AiCommentsListResponse"}}}}, "500": {"description": "Server Error"}}}}, "/v1/common-rule": {"get": {"tags": ["CommonRule"], "summary": "获取规则列表", "parameters": [{"$ref": "#/components/parameters/JWTToken"}, {"$ref": "#/components/parameters/Current"}, {"$ref": "#/components/parameters/PageSize"}], "responses": {"200": {"description": "成功获取规则列表", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonRuleListResponse"}}}}, "500": {"description": "服务器错误"}}}, "post": {"tags": ["CommonRule"], "summary": "创建新规则", "parameters": [{"$ref": "#/components/parameters/JWTToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonRule"}}}}, "responses": {"201": {"description": "成功创建规则", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonRule"}}}}, "500": {"description": "服务器错误"}}}, "put": {"tags": ["CommonRule"], "summary": "更新规则", "parameters": [{"$ref": "#/components/parameters/JWTToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonRule"}}}}, "responses": {"200": {"description": "成功更新规则", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonRule"}}}}, "500": {"description": "服务器错误"}}}, "delete": {"tags": ["CommonRule"], "summary": "删除规则", "parameters": [{"$ref": "#/components/parameters/JWTToken"}, {"name": "id", "in": "path", "required": true, "description": "规则ID", "schema": {"type": "integer", "example": 1}}], "responses": {"200": {"description": "成功删除规则"}, "500": {"description": "服务器错误"}}}}, "/v1/custom-rule": {"get": {"tags": ["CustomRule"], "summary": "获取自定义规则列表", "parameters": [{"$ref": "#/components/parameters/JWTToken"}, {"$ref": "#/components/parameters/Current"}, {"$ref": "#/components/parameters/PageSize"}], "responses": {"200": {"description": "成功获取自定义规则列表", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomRuleListResponse"}}}}, "500": {"description": "服务器错误"}}}, "post": {"tags": ["CustomRule"], "summary": "创建自定义规则", "parameters": [{"$ref": "#/components/parameters/JWTToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomRule"}}}}, "responses": {"201": {"description": "成功创建自定义规则", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomRule"}}}}, "500": {"description": "服务器错误"}}}, "put": {"tags": ["CustomRule"], "summary": "更新自定义规则", "parameters": [{"$ref": "#/components/parameters/JWTToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomRule"}}}}, "responses": {"200": {"description": "成功更新自定义规则", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomRule"}}}}, "500": {"description": "服务器错误"}}}, "delete": {"tags": ["CustomRule"], "summary": "删除自定义规则", "parameters": [{"$ref": "#/components/parameters/JWTToken"}], "responses": {"200": {"description": "成功删除自定义规则"}, "500": {"description": "服务器错误"}}}}, "/v1/gitlab-info": {"get": {"tags": ["GitLab Info"], "summary": "获取 GitLab 配置信息", "parameters": [{"$ref": "#/components/parameters/JWTToken"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GitLabInfoListResponse"}}}}, "500": {"description": "Server Error"}}}, "post": {"tags": ["GitLab Info"], "summary": "创建 GitLab 配置信息", "parameters": [{"$ref": "#/components/parameters/JWTToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GitLabInfoRequestBody"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GitLabInfoResponse"}}}}, "500": {"description": "Server Error"}}}, "put": {"tags": ["GitLab Info"], "summary": "更新 GitLab 配置信息", "parameters": [{"$ref": "#/components/parameters/JWTToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GitLabInfoPutRequestBody"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GitLabInfoResponse"}}}}, "500": {"description": "Server Error"}}}, "delete": {"tags": ["GitLab Info"], "summary": "删除 GitLab 配置信息", "parameters": [{"$ref": "#/components/parameters/JWTToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["id"], "properties": {"id": {"description": "待删除的 GitLab 配置 ID", "type": "number"}}}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GitLabInfoResponse"}}}}, "500": {"description": "Server Error"}}}}, "/v1/permission/assertRolePermission": {"post": {"tags": ["Permission"], "summary": "给角色分配资源权限", "parameters": [{"$ref": "#/components/parameters/JWTToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PermissionRequestBody"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/v1/permission/assertUserRole": {"post": {"tags": ["Permission"], "summary": "给用户分配角色", "parameters": [{"$ref": "#/components/parameters/JWTToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PermissionRequestBody"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/v1/permission/cancelRolePermission": {"post": {"tags": ["Permission"], "summary": "取消角色已分配的资源权限", "parameters": [{"$ref": "#/components/parameters/JWTToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PermissionRequestBody"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/v1/permission/cancelUserRole": {"post": {"tags": ["Permission"], "summary": "取消分配给用户的角色", "parameters": [{"$ref": "#/components/parameters/JWTToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PermissionRequestBody"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/v1/permission/getRolePermissions": {"post": {"tags": ["Permission"], "summary": "获取角色分配资源权限", "parameters": [{"$ref": "#/components/parameters/JWTToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PermissionRequestBody"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/v1/permission/getSelfPermissions": {"post": {"tags": ["Permission"], "summary": "获取用户自身的所有权限", "parameters": [{"$ref": "#/components/parameters/JWTToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PermissionRequestBody"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "500": {"description": "Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/v1/project": {"get": {"tags": ["Project"], "summary": "项目组", "parameters": [{"$ref": "#/components/parameters/JWTToken"}, {"$ref": "#/components/parameters/Namespace"}, {"$ref": "#/components/parameters/Current"}, {"$ref": "#/components/parameters/PageSize"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleListResponse"}}}}, "500": {"description": "Server Error"}}}, "post": {"tags": ["Project"], "summary": "创建项目", "parameters": [{"$ref": "#/components/parameters/JWTToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectRequestBody"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PermissionResponse"}}}}, "500": {"description": "Server Error"}, "default": {"description": "Server Error"}}}, "put": {"tags": ["Project"], "summary": "更新项目组", "parameters": [{"$ref": "#/components/parameters/JWTToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PutProjectRequestBody"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"ret_code": {"type": "integer", "example": 0, "description": "返回码"}, "message": {"type": "string", "example": "成功", "description": "返回消息"}, "data": {"type": "array", "items": {"type": "integer", "example": 1}, "description": "返回的项目组ID数组"}}}}}}, "500": {"description": "Server Error"}}}, "delete": {"tags": ["Project"], "summary": "删除项目组", "parameters": [{"$ref": "#/components/parameters/JWTToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["id"], "properties": {"id": {"description": "待删除项目组ID", "type": "number"}, "namespace": {"description": "待删除项目组", "type": "string"}}}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"ret_code": {"type": "integer", "example": 0, "description": "返回码"}, "message": {"type": "string", "example": "成功", "description": "返回消息"}}}}}}, "500": {"description": "Server Error"}}}}, "schemes": {"0": "https", "1": "http"}, "/v1/resource": {"get": {"tags": ["Resource"], "summary": "资源", "parameters": [{"$ref": "#/components/parameters/JWTToken"}, {"$ref": "#/components/parameters/NamespaceQuery"}, {"$ref": "#/components/parameters/ResourceQuery"}, {"$ref": "#/components/parameters/NameQuery"}, {"$ref": "#/components/parameters/CategoryQuery"}, {"$ref": "#/components/parameters/PaginationQuery"}, {"$ref": "#/components/parameters/PageSizeQuery"}], "responses": {"200": {"description": "OK"}, "500": {"description": "Server Error"}}}, "post": {"tags": ["Resource"], "summary": "添加资源", "parameters": [{"$ref": "#/components/parameters/JWTToken"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResourceRequestBody"}}}}, "responses": {"200": {"description": "OK"}, "500": {"description": "Server Error"}}}, "put": {"tags": ["Resource"], "summary": "更新资源", "parameters": [{"$ref": "#/components/parameters/JWTToken"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateResourceRequestBody"}}}}, "responses": {"200": {"description": "更新资源成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"ret_code": {"type": "number", "example": 0}, "message": {"type": "string", "example": ""}, "data": {"type": "array", "example": []}}}}}}, "500": {"description": "Server Error"}}}, "delete": {"tags": ["Resource"], "summary": "删除资源", "parameters": [{"$ref": "#/components/parameters/JWTToken"}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["id", "namespace"], "properties": {"id": {"type": "number", "description": "资源ID"}, "namespace": {"type": "string", "description": "所属项目组"}}}}}}, "responses": {"200": {"description": "OK"}, "500": {"description": "Server Error"}}}}, "/v1/role": {"get": {"tags": ["Role"], "summary": "获取角色列表", "parameters": [{"$ref": "#/components/parameters/JWTToken"}, {"$ref": "#/components/parameters/NamespaceQuery"}, {"$ref": "#/components/parameters/RoleQuery"}, {"$ref": "#/components/parameters/ResourceQuery"}, {"$ref": "#/components/parameters/NameQuery"}, {"$ref": "#/components/parameters/PaginationQuery"}, {"$ref": "#/components/parameters/PageSizeQuery"}], "responses": {"200": {"description": "OK"}, "500": {"description": "Server Error"}}}, "post": {"tags": ["Role"], "summary": "创建角色并赋予其操作", "parameters": [{"$ref": "#/components/parameters/JWTToken"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleRequestBody"}}}}, "responses": {"200": {"description": "OK"}, "500": {"description": "Server Error"}}}, "put": {"tags": ["Role"], "summary": "更新角色信息", "parameters": [{"$ref": "#/components/parameters/JWTToken"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateRoleRequestBody"}}}}, "responses": {"200": {"description": "OK"}, "500": {"description": "Server Error"}}}, "delete": {"tags": ["Role"], "summary": "删除角色", "parameters": [{"$ref": "#/components/parameters/JWTToken"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteRoleRequestBody"}}}}, "responses": {"200": {"description": "OK"}, "500": {"description": "Server Error"}}}}, "servers": {"0": {"url": "https://api.example.com"}, "1": {"url": "http://api.example.com"}}, "/v1/user": {"get": {"tags": ["User"], "summary": "获取用户列表", "parameters": [{"in": "header", "name": "jwt_token", "schema": {"type": "string"}, "required": true}, {"in": "query", "name": "namespace", "description": "项目名称", "required": true, "schema": {"type": "string"}}, {"in": "query", "name": "id", "description": "用户ID", "required": false, "schema": {"type": "number"}}, {"in": "query", "name": "user", "description": "用户名", "required": false, "schema": {"type": "string"}}, {"in": "query", "name": "<PERSON><PERSON><PERSON>", "description": "支持角色名称查询用户", "required": false, "schema": {"type": "string"}, "default": ""}, {"in": "query", "name": "userName", "description": "支持用户名称查询用户", "required": false, "schema": {"type": "string"}, "default": ""}, {"in": "query", "name": "current", "description": "页码", "default": 1, "schema": {"type": "number"}}, {"in": "query", "name": "pageSize", "description": "每页条数", "default": 10, "schema": {"type": "number"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number", "description": "用户ID"}, "userName": {"type": "string", "description": "用户名"}, "roleName": {"type": "string", "description": "角色名称"}, "email": {"type": "string", "description": "用户的电子邮件"}}}}, "current": {"type": "number", "description": "当前页码"}, "pageSize": {"type": "number", "description": "每页条数"}, "total": {"type": "number", "description": "总记录数"}}}}}}, "500": {"description": "Server Error"}}}, "post": {"tags": ["User"], "summary": "新增用户", "parameters": [{"name": "jwt_token", "in": "header", "schema": {"type": "string"}, "required": true}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["namespace", "user", "name"], "properties": {"name": {"type": "string"}, "namespace": {"type": "string"}, "user": {"type": "string"}, "job": {"type": "string"}, "password": {"type": "string", "default": "12345678"}, "email": {"type": "string"}, "phone_number": {"type": "string"}, "role_ids": {"type": "array", "items": {"type": "number"}}}}}}}, "responses": {"200": {"description": "OK"}, "500": {"description": "Server Error"}}}, "put": {"tags": ["User"], "summary": "更新用户信息", "parameters": [{"in": "header", "name": "jwt_token", "schema": {"type": "string"}, "required": true}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["id", "namespace"], "properties": {"id": {"type": "number"}, "namespace": {"type": "string", "comments": "必选，不可更改"}, "password": {"type": "string"}, "name": {"type": "string"}, "phone_number": {"type": "string"}, "job": {"type": "string"}, "email": {"type": "string"}, "role_ids": {"type": "array", "items": {"type": "number"}}}}}}}, "responses": {"200": {"description": "OK"}, "500": {"description": "Server Error"}}}, "delete": {"tags": ["User"], "summary": "删除用户", "parameters": [{"in": "header", "name": "jwt_token", "schema": {"type": "string"}, "required": true}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["namespace", "user", "name"], "properties": {"namespace": {"type": "string"}, "id": {"type": "number"}, "user": {"type": "string"}}}}}}, "responses": {"200": {"description": "OK"}, "500": {"description": "Server Error"}}}}, "/v1/userInfo": {"get": {"tags": ["User"], "summary": "获取用户信息", "parameters": [{"in": "header", "name": "jwt_token", "schema": {"type": "string"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"ret_code": {"type": "number", "description": "返回码", "default": 0}, "message": {"type": "string", "description": "返回信息"}, "data": {"type": "object", "properties": {"id": {"type": "number", "description": "用户ID"}, "namespace": {"type": "string", "description": "项目组名称"}, "user": {"type": "string", "description": "用户名"}, "name": {"type": "string", "description": "用户姓名"}, "job": {"type": "string", "description": "用户职位"}, "phone_number": {"type": "string", "description": "用户电话号码"}, "email": {"type": "string", "description": "用户邮箱"}, "roleList": {"type": "array", "items": {"$ref": "#/components/schemas/Role"}}}}}}}}}, "500": {"description": "Server Error"}}}}, "/v1/login": {"post": {"tags": ["User"], "summary": "登陆", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["user", "namespace", "password"], "properties": {"namespace": {"type": "string", "comments": "项目组名称"}, "user": {"type": "string", "comments": "用户名"}, "password": {"type": "string", "comments": "密码"}}}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "object", "properties": {"jwt_token": {"type": "string", "description": "jwt token"}, "user": {"type": "string", "description": "用户名"}}}, "ret_code": {"type": "number", "description": "返回码", "example": 0}, "message": {"type": "string", "description": "返回信息", "example": "ok"}}}}}}, "500": {"description": "Server Error"}}}}, "/v1/webhook/merge": {"post": {"tags": ["Webhook"], "summary": "监听Gitlab仓库合并事件", "parameters": [{"$ref": "#/components/parameters/XGitlabToken"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "object", "properties": {"projectId": {"type": "string", "description": "项目ID", "example": 1}, "mergeRequestId": {"type": "string", "description": "merge Id", "example": 1}, "comment": {"type": "string", "description": "ai 评论"}}}, "ret_code": {"type": "number", "description": "返回码", "example": 0}, "message": {"type": "string", "description": "返回信息", "example": "ok"}}}}}}, "500": {"description": "Server Error"}, "default": {"description": "Server Error"}}}}}, "components": {"parameters": {"JWTToken": {"name": "jwt_token", "in": "header", "schema": {"type": "string"}, "required": true, "description": "JWT 令牌"}, "Current": {"name": "current", "in": "query", "schema": {"type": "number", "default": 1}, "description": "页码"}, "PageSize": {"name": "pageSize", "in": "query", "schema": {"type": "number", "default": 10}, "description": "每页条数"}, "Namespace": {"name": "namespace", "in": "query", "schema": {"type": "string"}, "required": false, "description": "项目组名称"}, "NamespaceQuery": {"name": "namespace", "in": "query", "schema": {"type": "string"}, "required": true, "description": "项目组名称"}, "ResourceQuery": {"name": "resource", "in": "query", "schema": {"type": "string"}, "description": "资源", "required": false}, "NameQuery": {"name": "name", "in": "query", "schema": {"type": "string"}, "description": "角色名称", "required": false}, "CategoryQuery": {"name": "category", "in": "query", "schema": {"type": "string"}, "description": "类别", "default": "API,Menu,Action,Other"}, "PaginationQuery": {"name": "current", "in": "query", "schema": {"type": "number"}, "default": 1, "description": "页码"}, "PageSizeQuery": {"name": "pageSize", "in": "query", "schema": {"type": "number"}, "default": 10, "description": "每页条数"}, "RoleQuery": {"in": "query", "name": "role", "schema": {"type": "string"}, "required": false, "description": "指定角色"}, "XGitlabToken": {"name": "X-Gitlab-Token", "in": "header", "schema": {"type": "string"}, "required": true, "description": "webhook令牌"}}, "schemas": {"AiManager": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "model": {"type": "string", "example": "GPT-3"}, "api": {"type": "string", "example": "openai"}, "api_key": {"type": "string", "example": "sk-xxxxxx"}, "status": {"type": "integer", "enum": [1, -1], "example": 1}, "expired": {"type": "integer", "example": 1728401258, "description": "过期时间"}, "create_time": {"type": "integer", "example": 1728401258, "description": "创建时间"}, "update_time": {"type": "integer", "example": 1728401258, "description": "更新时间"}, "created_by": {"type": "string", "example": "admin"}, "updated_by": {"type": "string", "example": "admin"}}}, "AiManagerRequestBody": {"type": "object", "required": ["name", "model", "api", "api_key", "status", "expired"], "properties": {"name": {"type": "string", "description": "AI 管理名称"}, "model": {"type": "string", "description": "AI 模型名称"}, "api": {"type": "string", "description": "API 名称"}, "api_key": {"type": "string", "description": "API 密钥"}, "status": {"type": "integer", "enum": [1, -1], "example": 1}, "expired": {"type": "integer", "example": 1728401258, "description": "过期时间"}}}, "AiManagerPutRequestBody": {"type": "object", "required": ["id"], "properties": {"id": {"type": "integer", "description": "AI 管理 ID"}, "model": {"type": "string"}, "api": {"type": "string"}, "api_key": {"type": "string"}, "status": {"type": "integer", "enum": [1, -1]}, "expired": {"type": "integer", "example": 1728401258, "description": "过期时间"}}}, "AiManagerResponse": {"type": "object", "properties": {"ret_code": {"type": "integer", "example": 0}, "message": {"type": "string", "example": "成功"}, "data": {"$ref": "#/components/schemas/AiManager"}}}, "AiManagerListResponse": {"type": "object", "properties": {"ret_code": {"type": "integer", "example": 0}, "message": {"type": "string", "example": "成功"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/AiManager"}}, "total": {"type": "integer", "example": 5}}}, "AiMessage": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "project_id": {"type": "string", "example": "1"}, "merge_id": {"type": "string", "example": "123"}, "ai_model": {"type": "string", "example": "DeepSeek"}, "rule": {"type": "integer", "enum": [1, 2], "example": 1}, "rule_id": {"type": "integer", "example": 10}, "result": {"type": "string", "example": "Rule passed successfully"}, "passed": {"type": "boolean", "example": false}, "checked_by": {"type": "string", "example": "auto-checker"}, "create_time": {"type": "integer", "example": 1728401258, "description": "创建时间"}}}, "AiCommentsListResponse": {"type": "object", "properties": {"ret_code": {"type": "integer", "example": 0}, "message": {"type": "string", "example": "成功"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/AiMessage"}}, "total": {"type": "integer", "example": 10}}}, "CommonRule": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "Rule 1"}, "language": {"type": "string", "enum": ["Python", "Java", "JavaScript", "Go", "<PERSON>", "C++", "other"], "example": "Python"}, "rule": {"type": "string", "example": "def check_code_quality(): # Rule logic here"}, "description": {"type": "string", "example": "This is a rule to check code quality."}}}, "CommonRuleListResponse": {"type": "object", "properties": {"ret_code": {"type": "integer", "example": 0}, "message": {"type": "string", "example": "成功"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/CommonRule"}}, "total": {"type": "integer", "example": 5}}}, "CustomRule": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "project_name": {"type": "string", "example": "Project A"}, "project_id": {"type": "integer", "example": 101}, "rule": {"type": "string", "example": "def custom_rule(): # Custom rule logic here"}, "status": {"type": "integer", "enum": [1, -1], "example": 1}}}, "CustomRuleListResponse": {"type": "object", "properties": {"ret_code": {"type": "integer", "example": 0}, "message": {"type": "string", "example": "成功"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/CustomRule"}}, "total": {"type": "integer", "example": 5}}}, "GitLabInfo": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "api": {"type": "string", "example": "GitLab API v4"}, "token": {"type": "string", "example": "gitlab-access-token"}, "status": {"type": "integer", "enum": [1, -1], "example": 1}, "gitlab_version": {"type": "string", "example": "14.0.0"}, "expired": {"type": "integer", "example": 1728401258, "description": "过期时间"}, "gitlab_url": {"type": "string", "example": "https://gitlab.example.com"}, "create_time": {"type": "integer", "example": 1728401258, "description": "过期时间"}, "update_time": {"type": "integer", "example": 1728401258, "description": "更新时间"}}}, "GitLabInfoRequestBody": {"type": "object", "required": ["api", "token", "status", "gitlab_version", "expired", "gitlab_url"], "properties": {"api": {"type": "string", "description": "GitLab API 名称"}, "token": {"type": "string", "description": "GitLab 访问令牌"}, "status": {"type": "integer", "enum": [1, -1], "description": "状态: 1启用 -1禁用"}, "gitlab_version": {"type": "string", "description": "GitLab <PERSON>本"}, "expired": {"type": "integer", "example": 1728401258, "description": "过期时间"}, "gitlab_url": {"type": "string", "description": "GitLab 服务器地址"}}}, "GitLabInfoPutRequestBody": {"type": "object", "required": ["id"], "properties": {"id": {"type": "integer", "description": "GitLab 配置信息 ID"}, "api": {"type": "string"}, "token": {"type": "string"}, "status": {"type": "string", "enum": [1, -1]}, "gitlab_version": {"type": "string"}, "expired": {"type": "integer", "example": 1728401258, "description": "过期时间"}, "gitlab_url": {"type": "string"}}}, "GitLabInfoResponse": {"type": "object", "properties": {"ret_code": {"type": "integer", "example": 0}, "message": {"type": "string", "example": "成功"}, "data": {"$ref": "#/components/schemas/GitLabInfo"}}}, "GitLabInfoListResponse": {"type": "object", "properties": {"ret_code": {"type": "integer", "example": 0}, "message": {"type": "string", "example": "成功"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/GitLabInfo"}}, "total": {"type": "integer", "example": 5}}}, "CommonResponse": {"type": "object", "properties": {"ret_code": {"type": "integer", "example": 0}, "message": {"type": "string", "example": "成功"}, "data": {"type": "object"}}}, "PermissionRequestBody": {"type": "object", "properties": {"namespace": {"description": "项目组标识", "type": "string"}, "role_id": {"description": "角色 ID", "type": "number"}, "user": {"description": "用户标识", "type": "string"}, "resource_id": {"description": "资源 ID", "type": "number"}, "resource_ids": {"description": "资源 ID 集合", "type": "array", "items": {"type": "number"}}, "describe": {"description": "描述", "type": "string"}, "role_ids": {"type": "array", "items": {"type": "number"}}, "current": {"description": "页码", "type": "number", "default": 1}, "pageSize": {"description": "每页条数", "type": "number", "default": 10}}}, "ErrorResponse": {"type": "object", "properties": {"ret_code": {"type": "integer", "example": 500}, "message": {"type": "string", "example": "Server Error"}}}, "SuccessResponse": {"allOf": [{"$ref": "#/components/schemas/CommonResponse"}, {"type": "object", "properties": {"data": {"oneOf": [{"type": "array", "items": {"type": "object"}}, {"type": "object"}, {"type": "number"}, {"type": "string"}]}}}]}, "Namespace": {"type": "object", "properties": {"id": {"type": "integer", "example": 30}, "namespace": {"type": "string", "example": "acl10"}, "parent": {"type": "string", "example": ""}, "name": {"type": "string", "example": "heng.du10"}, "describe": {"type": "string", "example": "1"}, "operator": {"type": "string", "example": "pengfei.lv"}, "create_time": {"type": "integer", "example": 1721534729}, "update_time": {"type": "integer", "example": 1721534729}}}, "RoleListResponse": {"type": "object", "properties": {"ret_code": {"type": "integer", "example": 0}, "message": {"type": "string", "example": "成功"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/Namespace"}}, "total": {"type": "integer", "example": 13}}}, "ProjectRequestBody": {"type": "object", "required": ["namespace", "name"], "properties": {"namespace": {"type": "string", "description": "所属项目组"}, "parent": {"type": "string", "description": "父级项目组", "default": ""}, "name": {"type": "string", "description": "项目组名称"}, "describe": {"type": "string", "description": "项目组描述"}}}, "PermissionResponse": {"type": "object", "properties": {"ret_code": {"type": "integer", "example": 0, "description": "返回码"}, "message": {"type": "string", "example": "成功", "description": "返回消息"}, "data": {"type": "object", "properties": {"parent": {"type": "string", "example": "", "description": "父级权限"}, "id": {"type": "integer", "example": 31, "description": "权限ID"}, "namespace": {"type": "string", "example": "ops", "description": "命名空间"}, "name": {"type": "string", "example": "运营权限", "description": "权限名称"}, "operator": {"type": "string", "example": "pengfei.lv", "description": "操作人"}, "describe": {"type": "string", "example": "测试", "description": "权限描述"}, "create_time": {"type": "integer", "example": 1728401258, "description": "创建时间"}, "update_time": {"type": "integer", "example": 1728401258, "description": "更新时间"}}}}}, "PutProjectRequestBody": {"type": "object", "required": ["id"], "properties": {"id": {"type": "number", "description": "项目组ID", "example": 1}, "namespace": {"type": "string", "description": "所属项目组", "example": "acl"}, "name": {"type": "string", "description": "项目组名称", "example": "项目组名称示例"}, "describe": {"type": "string", "description": "项目组描述", "example": "项目组描述示例"}}}, "ResourceRequestBody": {"type": "object", "required": ["namespace", "resource", "category", "name"], "properties": {"namespace": {"type": "string", "description": "所属项目组"}, "resource": {"type": "string", "description": "资源标识"}, "category": {"type": "string", "description": "资源分类", "enum": ["API", "<PERSON><PERSON>", "Action", "Other"]}, "name": {"type": "string", "description": "资源名"}, "describe": {"type": "string", "description": "描述"}}}, "UpdateResourceRequestBody": {"type": "object", "required": ["id", "namespace"], "properties": {"id": {"type": "number", "description": "资源ID"}, "namespace": {"type": "string", "description": "所属项目组"}, "category": {"type": "string", "enum": ["API", "<PERSON><PERSON>", "Action", "Other"], "description": "资源分类"}, "resource": {"type": "string", "description": "资源标识"}, "name": {"type": "string", "description": "资源名"}, "describe": {"type": "string", "description": "描述"}}}, "RoleRequestBody": {"type": "object", "required": ["namespace", "role", "name"], "properties": {"namespace": {"type": "string", "description": "所属项目组"}, "role": {"type": "string", "description": "角色标识"}, "name": {"type": "string", "description": "角色名"}, "describe": {"type": "string", "description": "描述"}, "permissions": {"type": "array", "description": "角色权限", "items": {"type": "object", "properties": {"resource_id": {"type": "number", "description": "资源标识符"}, "describe": {"type": "string", "description": "权限描述"}}}}}}, "UpdateRoleRequestBody": {"type": "object", "required": ["id", "namespace", "role", "permissions"], "properties": {"id": {"type": "number", "description": "角色ID"}, "namespace": {"type": "string", "description": "所属项目组"}, "role": {"type": "string", "description": "待更新角色"}, "permissions": {"type": "array", "items": {"type": "object", "properties": {"resource_id": {"type": "number", "description": "资源标识符"}, "describe": {"type": "string", "description": "权限描述"}}}}, "name": {"type": "string", "description": "角色名"}, "describe": {"type": "string", "description": "描述"}}}, "DeleteRoleRequestBody": {"type": "object", "required": ["id", "namespace"], "properties": {"id": {"type": "number", "description": "待删除角色ID"}, "namespace": {"type": "string", "description": "所属项目组"}}}, "Role": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "namespace": {"type": "string", "example": "acl"}, "role": {"type": "string", "example": "admin"}, "name": {"type": "string", "example": "超级管理员"}, "describe": {"type": "string", "example": "最高权限"}, "operator": {"type": "string", "example": "pengfei.lv"}, "create_time": {"type": "integer", "example": 1719460635}, "update_time": {"type": "integer", "example": 1719460635}}}}}, "tags": [{"name": "Project", "description": "项目组"}, {"name": "Resource", "description": "权限资源管理"}, {"name": "Role", "description": "角色管理"}, {"name": "Permission", "description": "权限"}, {"name": "User", "description": "登录注册"}, {"name": "Webhook", "description": "webhook"}, {"name": "AI Manager", "description": "AI 管理"}, {"name": "CommonRule", "description": "通用规则"}, {"name": "CustomRule", "description": "自定义规则"}]}