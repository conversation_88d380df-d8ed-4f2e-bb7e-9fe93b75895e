{"name": "code-review", "description": "Frontend of Monto Acl", "private": true, "version": "1.0.0", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint ./src --ext ts,tsx --fix", "prettier": "pnpm exec prettier . --write", "svgo": "svgo -f src/assets/iconsvg --config=src/assets/iconsvg/svgo.yml", "preinstall": "only-allow pnpm", "prepare": "husky"}, "dependencies": {"@ant-design/icons": "^5.3.7", "@babel/core": ">=7.0.0-0 <8.0.0", "@babel/plugin-syntax-flow": "^7.14.5", "@babel/plugin-transform-react-jsx": "^7.14.9", "antd": "^5", "axios": "^0.27.2", "classnames": "^2.3.1", "dayjs": "^1.11.12", "echarts": "^5.3.3", "github-markdown-css": "^5.8.1", "lodash": "^4.17.21", "mobx": "^6.13.0", "mobx-react-lite": "^4.0.7", "query-string": "^7.1.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-markdown": "^10.1.0", "react-router-dom": "^6.3.0", "remark-gfm": "^4.0.1"}, "devDependencies": {"@types/antd": "^1.0.0", "@types/lodash": "^4.14.182", "@types/react": "^18.0.15", "@types/react-dom": "^18.0.6", "@types/react-router-dom": "^5.3.3", "@vitejs/plugin-react": "^2.0.0", "eslint": "^8.20.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.5.0", "eslint-config-react-app": "^7.0.1", "eslint-plugin-import": "^2.26.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.30.1", "husky": "^9.1.4", "less": "^4.1.3", "mockjs": "^1.1.0", "only-allow": "^1.1.1", "prettier": "^2.7.1", "svgo": "^1.3.2", "typescript": "^5.7.3", "vite": "^4.4.9", "vite-plugin-mock": "^2.9.6", "vite-plugin-svg-icons": "^2.0.1"}, "engines": {"node": ">= 14.18.0"}, "keywords": ["monto", "acl", "react", "typescript", "admin", "template", "antd", "Ant Design", "vite", "mobx"]}