# Code Review 系统部署说明

## ✅ 问题已修复

**问题**：前端调用 API 时端口错误，如 `http://*************:9003/ai-manager` 应该是 `http://*************:9000`

**解决方案**：
✅ **已移除构建时环境变量打包**：不再将 `VITE_APP_APIHOST` 打包到前端镜像中
✅ **运行时动态加载**：Docker 启动时从 `.env` 文件动态加载环境变量
✅ **修复所有 service 文件**：所有前端 service 文件都已使用 `getApiHost()` 函数
✅ **使用指定配置文件**：启动时使用 `docker-compose.mysql.yml`

### 主要改进

1. **前端环境变量动态加载**: 前端不再在构建时写入 API 地址，而是在容器启动时动态生成配置文件
2. **大模型配置完全可编辑**: name、api_url、model 字段都可以在界面中编辑
3. **支持完整的 CRUD 操作**: 大模型配置支持增加、编辑、删除操作

### 部署步骤

#### 1. 配置环境变量

编辑 `.env` 文件：

```bash
# 部署IP
IP=*************

# CONSOLE
CONSOLE_PORT=9003

# API
NODE_ENV=production
API_PORT=9000

# DB: mysql
DB_TYPE=mysql
DB_HOST=mysql
DB_PORT=3306
DB_USER=root
DB_PASSWORD=mysql#123456
DB_DATABASE=ucode_review

# Frontend API Host (动态加载，不打包到镜像中)
VITE_APP_APIHOST=http://${IP}:${API_PORT}/v1
```

#### 2. 启动服务

使用 MySQL 数据库：

```bash
# 方法1: 使用启动脚本（推荐）
chmod +x start-with-mysql.sh
./start-with-mysql.sh

# 如果 pnpm 构建失败，使用 npm 版本
./start-with-mysql.sh npm

# 方法2: 直接使用 docker-compose
docker-compose -f docker-compose.mysql.yml up -d

# 或者使用 npm 版本
docker-compose -f docker-compose.mysql.npm.yml up -d
```

#### 3. 访问系统

- 前端界面: http://*************:9003
- API 接口: http://*************:9000

### 大模型配置

系统启动后，可以在前端界面中配置大模型：

1. 访问 "AI 代码检查" -> "AI 模型管理"
2. 点击 "新增" 按钮
3. 填写以下信息：
   - **模型名称**: 如 GPT-4、Claude-3、DeepSeek 等
   - **API 地址**: 如 https://api.openai.com/v1
   - **API 密钥**: 从对应平台获取的 API Key
   - **模型标识**: 如 gpt-4、claude-3-sonnet、deepseek-ai/DeepSeek-V3 等

### 技术实现

#### 前端环境变量动态加载

1. **构建时**: 不再写入环境变量到 `.env.production` 文件
2. **运行时**: Docker 容器启动时生成 `/usr/share/nginx/html/config/env.js` 文件
3. **加载**: 前端通过 `window.ENV` 对象获取运行时配置

#### 环境变量获取优先级

```typescript
// 1. 优先使用运行时配置
if (window.ENV && window.ENV.VITE_APP_APIHOST) {
  return window.ENV.VITE_APP_APIHOST;
}

// 2. 回退到构建时环境变量
return import.meta.env.VITE_APP_APIHOST || '';
```

### 故障排除

#### 1. 前端构建失败

如果遇到 pnpm 依赖安装问题，可以尝试：

```bash
# 清理并重新构建
docker system prune -f
docker-compose -f docker-compose.mysql.yml build --no-cache frontend
```

或者修改 Dockerfile.prod 使用 npm 替代 pnpm：

```dockerfile
# 将 pnpm install 替换为
RUN npm install --legacy-peer-deps
RUN npm run build
```

#### 2. 前端无法连接后端

检查环境变量配置：

```bash
# 查看容器中的配置文件
docker exec -it <frontend-container-id> cat /usr/share/nginx/html/config/env.js
```

#### 3. 大模型配置无法保存

检查后端日志：

```bash
docker-compose -f docker-compose.mysql.yml logs backend
```

#### 4. 数据库连接失败

检查数据库状态：

```bash
docker-compose -f docker-compose.mysql.yml logs mysql
```

#### 5. 构建时依赖版本冲突

如果遇到依赖版本冲突，可以：

1. 使用 `--legacy-peer-deps` 标志
2. 或者更新 package.json 中的依赖版本
3. 或者使用 `--force` 强制安装

### 开发环境

开发环境仍然使用构建时环境变量，配置文件：`code-review-main/frontend/.env.development`

```bash
# 开发环境启动
cd code-review-main/frontend
pnpm install
pnpm dev
```
