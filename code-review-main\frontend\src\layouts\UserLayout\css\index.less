@import '../../../assets/css/index.less';
@import './var.less';
@import './mixins.less';

.user-layout {
  display: flex;
  width: 100%;
  height: 100vh;
  min-height: 500px;
  overflow: auto;
  background-image: url('../../../assets/images/background.png');
  background-repeat: no-repeat;
  background-position: center center;
  background-attachment: fixed;
  background-size: cover;
  align-items: center;

  .lang {
    position: absolute;
    top: 20px;
    right: 50px;
    color: #000000;
    font-size: 16px;
  }
}
