openapi: 3.0.0
info:
  title: "ACL-Pro"
  version: "1.0"

components:
  parameters:
    JWTToken:
      in: header
      name: jwt_token
      schema:
        type: string
      required: true
      description: JWT 令牌
    NamespaceQuery:
      in: query
      name: namespace
      schema:
        type: string
      required: true
      description: 项目组名称
    RoleQuery:
      in: query
      name: role
      schema:
        type: string
      required: false
      description: 指定角色
    ResourceQuery:
      in: query
      name: resource
      schema:
        type: string
      required: false
      description: 资源
    NameQuery:
      in: query
      name: name
      schema:
        type: string
      required: false
      description: 角色名称
    PaginationQuery:
      in: query
      name: current
      schema:
        type: number
      default: 1
      description: 页码
    PageSizeQuery:
      in: query
      name: pageSize
      schema:
        type: number
      default: 10
      description: 每页条数
  schemas:
    RoleRequestBody:
      type: object
      required:
        - namespace
        - role
        - name
      properties:
        namespace:
          type: string
          description: 所属项目组
        role:
          type: string
          description: 角色标识
        name:
          type: string
          description: 角色名
        describe:
          type: string
          description: 描述
        permissions:
          type: array
          description: 角色权限
          items:
            type: object
            properties:
              resource_id:
                type: number
                description: 资源标识符
              describe:
                type: string
                description: 权限描述
    UpdateRoleRequestBody:
      type: object
      required:
        - id
        - namespace
        - role
        - permissions
      properties:
        id:
          type: number
          description: 角色ID
        namespace:
          type: string
          description: 所属项目组
        role:
          type: string
          description: 待更新角色
        permissions:
          type: array
          items:
            type: object
            properties:
              resource_id:
                type: number
                description: 资源标识符
              describe:
                type: string
                description: 权限描述
        name:
          type: string
          description: 角色名
        describe:
          type: string
          description: 描述
    DeleteRoleRequestBody:
      type: object
      required:
        - id
        - namespace
      properties:
        id:
          type: number
          description: 待删除角色ID
        namespace:
          type: string
          description: 所属项目组

paths:
  /v1/role:
    get:
      tags: 
        - Role
      summary: "获取角色列表"
      parameters:
        - $ref: '#/components/parameters/JWTToken'
        - $ref: '#/components/parameters/NamespaceQuery'
        - $ref: '#/components/parameters/RoleQuery'
        - $ref: '#/components/parameters/ResourceQuery'
        - $ref: '#/components/parameters/NameQuery'
        - $ref: '#/components/parameters/PaginationQuery'
        - $ref: '#/components/parameters/PageSizeQuery'
      responses:
        '200':
          description: "OK"
        '500':
          description: "Server Error"

    post:
      tags: 
        - Role
      summary: "创建角色并赋予其操作"
      parameters:
        - $ref: '#/components/parameters/JWTToken'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RoleRequestBody'
      responses:
        '200':
          description: "OK"
        '500':
          description: "Server Error"

    put:
      tags: 
        - Role
      summary: "更新角色信息"
      parameters:
        - $ref: '#/components/parameters/JWTToken'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateRoleRequestBody'
      responses:
        '200':
          description: "OK"
        '500':
          description: "Server Error"

    delete:
      tags: 
        - Role
      summary: "删除角色"
      parameters:
        - $ref: '#/components/parameters/JWTToken'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeleteRoleRequestBody'
      responses:
        '200':
          description: "OK"
        '500':
          description: "Server Error"

servers:
  - url: https://api.example.com
  - url: http://api.example.com
