#!/bin/bash

echo "=== Code Review 系统端口诊断 ==="
echo ""

# 加载环境变量
if [ -f ".env" ]; then
    source .env
    echo "✅ 环境变量加载成功"
    echo "  IP: $IP"
    echo "  前端端口: $CONSOLE_PORT"
    echo "  API端口: $API_PORT"
    echo "  数据库端口: $DB_PORT"
else
    echo "❌ .env 文件不存在"
    exit 1
fi

echo ""
echo "=== 检查端口占用情况 ==="

# 检查端口占用
check_port() {
    local port=$1
    local service=$2
    
    if command -v netstat >/dev/null 2>&1; then
        result=$(netstat -tlnp 2>/dev/null | grep ":$port ")
    elif command -v ss >/dev/null 2>&1; then
        result=$(ss -tlnp 2>/dev/null | grep ":$port ")
    else
        echo "  无法检查端口 $port (缺少 netstat 或 ss 命令)"
        return
    fi
    
    if [ -n "$result" ]; then
        echo "  ❌ 端口 $port ($service) 已被占用:"
        echo "     $result"
    else
        echo "  ✅ 端口 $port ($service) 可用"
    fi
}

check_port $CONSOLE_PORT "前端"
check_port $API_PORT "API"
check_port $DB_PORT "数据库"

echo ""
echo "=== 检查 Docker 容器状态 ==="

if command -v docker >/dev/null 2>&1; then
    echo "Docker 容器列表:"
    docker ps -a --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    
    echo ""
    echo "=== 检查 Docker Compose 服务 ==="
    if [ -f "docker-compose.mysql.yml" ]; then
        echo "Docker Compose 服务状态:"
        docker-compose -f docker-compose.mysql.yml ps
    else
        echo "❌ docker-compose.mysql.yml 文件不存在"
    fi
else
    echo "❌ Docker 未安装或不可用"
fi

echo ""
echo "=== 测试 API 连接 ==="

# 测试 API 连接
test_api() {
    local url="http://$IP:$API_PORT/v1/webhook/merge"
    echo "测试 URL: $url"
    
    if command -v curl >/dev/null 2>&1; then
        echo "发送 GET 请求 (应该返回 404 或方法不允许):"
        curl -s -w "HTTP状态码: %{http_code}\n" "$url" | head -5
        
        echo ""
        echo "发送 POST 请求 (测试路由是否存在):"
        curl -s -w "HTTP状态码: %{http_code}\n" -X POST "$url" -H "Content-Type: application/json" -d '{}' | head -5
    else
        echo "❌ curl 未安装，无法测试 API 连接"
    fi
}

test_api

echo ""
echo "=== 建议的解决方案 ==="
echo "1. 如果端口被占用，请停止占用端口的服务或修改 .env 中的端口配置"
echo "2. 如果 Docker 容器未运行，请执行: docker-compose -f docker-compose.mysql.yml up -d"
echo "3. 如果 API 返回 404，请检查后端服务是否正常启动"
echo "4. GitLab webhook URL 应该配置为: http://$IP:$API_PORT/v1/webhook/merge"
