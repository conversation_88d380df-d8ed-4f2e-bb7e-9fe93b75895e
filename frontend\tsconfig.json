{"compilerOptions": {"target": "ESNext", "useDefineForClassFields": true, "types": ["vite/client"], "lib": ["DOM", "DOM.Iterable", "ESNext"], "allowJs": false, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "module": "ESNext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "baseUrl": ".", "paths": {"@/*": ["src/*"], "~/*": ["node_modules/*"]}}, "include": ["src"], "references": [{"path": "./tsconfig.node.json"}]}