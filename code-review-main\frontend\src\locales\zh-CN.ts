export default {
  'app.empty': '空空如也',
  'app.global.menu.notfound': '没有数据',
  'app.global.form.validatefields.catch': '验证不通过，请检查输入',
  'app.global.tip.delete.success': '删除成功',
  'app.global.tip.update.success': '更新成功',
  'app.global.tip.create.success': '创建成功',
  'app.global.close': '关闭',
  'app.global.delete': '删除',
  'app.global.edit': '编辑',
  'app.global.view': '查看',
  'app.global.delete.tip': '确认删除此条信息吗',
  'app.global.doing': '开发中',

  'app.form.required': '必填',
  'app.form.search': '搜索',
  'app.form.reset': '重置',
  'app.form.expand': '展开',
  'app.form.refresh': '刷新',

  'app.table.large': '宽松',
  'app.table.middle': '中等',
  'app.table.small': '紧凑',
  'app.table.lineheight': '行高',
  'app.table.fuzzysearch': '模糊搜索',
  'app.table.operator': '更新人',
  'app.table.updatetime': '更新时间',
  'app.table.action': '操作',

  'user-layout.menu.login': '登录',
  'user-layout.menu.register': '注册',

  'page.user.login.form-item-username': '用户名: admin',
  'page.user.login.form-item-username.required': '请输入用户名',
  'page.user.login.form-item-password': '密码: 12345678',
  'page.user.login.form-item-password.required': '请输入密码',
  'page.user.login.form.title': '账户登录',
  'page.user.login.form.btn-submit': '登录',
  'page.user.login.form.btn-jump': '还没有账户？现在注册！',
  'page.user.login.form.login-error': '用户名或密码错误！',
  'page.user.login.form.login-success': '登录成功！',

  'page.user.register.form-item-username': '用户名',
  'page.user.register.form-item-username.required': '请输入用户名',
  'page.user.register.form-item-password': '密码',
  'page.user.register.form-item-password.required': '请输入密码',
  'page.user.register.form-item-confirmpassword': '确认密码',
  'page.user.register.form-item-confirmpassword.compare': '您输入的两个密码不匹配！',
  'page.user.register.form.title': '注册账户',
  'page.user.register.form.btn-submit': '注册',
  'page.user.register.form.btn-jump': '已有账户？现在登录！',
  'page.user.register.form.register-success': '注册成功，请登录！',

  'page.home.text-day': '日',
  'page.home.text-week': '周',
  'page.home.text-month': '月',
  'page.home.text-years': '年',
  'page.home.text-total': '总数量',
  'page.home.text-daycompare': '日同比',
  'page.home.text-weekcompare': '周同比',
  'page.home.articlechartcard.card-title': '随笔',
  'page.home.workschartcard.card-title': '作品',
  'page.home.topicschartcard.card-title': '专题',
  'page.home.linkschartcard.card-title': '左邻右舍',
  'page.home.hotsearchcard.card-title': '热门搜索',
  'page.home.hotsearchcard.card.table-column-number': '序号',
  'page.home.hotsearchcard.card.table-column-name': '关键词',
  'page.home.hotsearchcard.card.table-column-hit': '次数',
  'page.home.hottagscard.card-title': '热门标签',
  'page.home.hottagscard.card.table-column-number': '序号',
  'page.home.hottagscard.card.table-column-name': '标签',
  'page.home.hottagscard.card.table-column-hit': '次数',
  'page.home.articlehitcard.card-title': '随笔浏览量排行',
  'page.home.articlehitcard.card.table-column-number': '序号',
  'page.home.articlehitcard.card.table-column-title': '标题',
  'page.home.articlehitcard.card.table-column-hit': '浏览量',
  'page.home.workshitcard.card-title': '作品浏览量排行',
  'page.home.workshitcard.card.table-column-number': '序号',
  'page.home.workshitcard.card.table-column-title': '标题',
  'page.home.workshitcard.card.table-column-hit': '浏览量',

  'universal-layout.topmenu.userinfo': '个人信息',
  'universal-layout.topmenu.logout': '退出',
  'universal-layout.menu.home': '首页',
  'universal-layout.menu.home.workplace': '工作台',
  'universal-layout.menu.home.custom-breadcrumbs.montoacl.cc': 'montoacl.cc',
  'universal-layout.menu.home.bookmark': '多彩书签',
  'universal-layout.menu.home.timestamp': '时间戳转换',

  'universal-layout.menu.roles': '权限管理',
  'universal-layout.menu.roles.user': '用户列表',
  'universal-layout.menu.roles.role': '角色列表',
  'universal-layout.menu.roles.resource': '资源列表',

  'universal-layout.menu.aicodecheck': 'AI代码检测',
  'universal-layout.menu.aicodecheck.commonrule': '通用规则',
  'universal-layout.menu.aicodecheck.customrule': '自定义规则',
  'universal-layout.menu.aicodecheck.commentlist': '评论列表',
  'universal-layout.menu.aicodecheck.AIModel': 'AI模型管理',
  'universal-layout.menu.aicodecheck.GitlabToken': 'Gitlab Token',

  'page.resource.name': '资源名称',
  'page.resource.key': '资源标识',
  'page.resource.category': '资源类别',
  'page.resource.describe': '描述',
  'page.resource.add': '创建资源',

  'page.role.name': '角色名称',
  'page.role.key': '角色标识',
  'page.role.describe': '描述',
  'page.role.configresource': '配置资源',
  'page.role.add': '创建角色',

  'page.user.enname': '英文名',
  'page.user.cnname': '中文名',
  'page.user.password': '密码',
  'page.user.role': '角色',
  'page.user.job': '职位',
  'page.user.email': '邮箱',
  'page.user.phone': '手机号',
  'page.user.add': '创建用户',
  'page.user.view': '查看用户',

  'page.aicodecheck.rule.name': '规则名称',
  'page.aicodecheck.rule.project_id': '规则隶属项目ID',
  'page.aicodecheck.rule.project_name': '规则隶属项目名称',
  'page.aicodecheck.rule.language': '语言',
  'page.aicodecheck.rule': '规则',
  'page.aicodecheck.rule.id': '规则 ID',
  'page.aicodecheck.rule.description': '规则描述',
  'page.aicodecheck.rule.add': '创建规则',
  'page.aicodecheck.comment.aimodel': 'AI 模型',
  'page.aicodecheck.comment.result': '审核结果',
  'page.aicodecheck.comment.createtime': '审核时间',
  'page.aicodecheck.comment.status': '审核状态',
  'page.aicodecheck.comment.status.pass': '通过',
  'page.aicodecheck.comment.status.fail': '不通过',

  'page.aicodecheck.aimodel.name': '模型名称',
  'page.aicodecheck.aimodel.model': '模型',
  'page.aicodecheck.aimodel.api_url': 'API地址',
  'page.aicodecheck.aimodel.api_key': 'API密钥',
  'page.aicodecheck.aimodel.add': '添加模型',

  'page.aicodecheck.gitlab.add': '添加TOKEN',
  'page.aicodecheck.gitlab.api': 'API',
  'page.aicodecheck.gitlab.token': 'Token',
  'page.aicodecheck.gitlab.expired': '有效期',
  'page.aicodecheck.gitlab.webhook_name': 'webhook名称',
  'page.aicodecheck.gitlab.webhook_url': 'webhook地址',
  'page.aicodecheck.gitlab.source_branch': '源分支',
  'page.aicodecheck.gitlab.target_branch': '目标分支',

  'page.aicodecheck.comment.human_rating': '评分',
  'page.aicodecheck.comment.remark': '备注',
  'setting.pagestyle': '页面风格',
  'setting.navigationmode': '导航风格',
  'setting.headfixed': '固定头部',
  'setting.navtabs': 'tab导航',

  'test.menuacl': '测试菜单权限',
  'test.pageacl': '测试页面权限',
  'test.apiacl': '测试API权限',
};
