export default {
  'app.empty': 'empty',
  'app.global.menu.notfound': 'not found',
  'app.global.form.validatefields.catch': 'the validation did not pass, please check the input',
  'app.global.tip.delete.success': 'delete successfully',
  'app.global.tip.update.success': 'update successfully',
  'app.global.tip.create.success': 'create successfully',
  'app.global.close': 'close',
  'app.global.delete': 'delete',
  'app.global.edit': 'edit',
  'app.global.view': 'view',
  'app.global.delete.tip': 'are you sure to delete',
  'app.global.doing': 'developing',

  'app.form.required': 'required',
  'app.form.search': 'search',
  'app.form.reset': 'reset',
  'app.form.expand': 'expand',
  'app.form.refresh': 'refresh',

  'app.table.large': 'large',
  'app.table.middle': 'middle',
  'app.table.small': 'small',
  'app.table.lineheight': 'line height',
  'app.table.fuzzysearch': 'fuzzy search',
  'app.table.operator': 'operator',
  'app.table.updatetime': 'update time',
  'app.table.action': 'action',

  'user-layout.menu.login': 'login',
  'user-layout.menu.register': 'register',

  'page.user.login.form-item-username': 'username: admin',
  'page.user.login.form-item-username.required': 'please input your username',
  'page.user.login.form-item-password': 'password: ********',
  'page.user.login.form-item-password.required': 'please input your password',
  'page.user.login.form.title': 'account login',
  'page.user.login.form.btn-submit': 'sign in',
  'page.user.login.form.btn-jump': 'or register now!',
  'page.user.login.form.login-error': 'wrong username or password!',
  'page.user.login.form.login-success': 'login successful!',

  'page.user.register.form-item-username': 'user name',
  'page.user.register.form-item-username.required': 'please input your user name',
  'page.user.register.form-item-password': 'password',
  'page.user.register.form-item-password.required': 'please input your password',
  'page.user.register.form-item-confirmpassword': 'confirm password',
  'page.user.register.form-item-confirmpassword.compare': 'the two passwords that you entered do not match!',
  'page.user.register.form.title': 'account registration',
  'page.user.register.form.btn-submit': 'register',
  'page.user.register.form.btn-jump': 'already have an account?',
  'page.user.register.form.register-success': 'registered successfully, please log in!',

  'page.home.text-day': 'day',
  'page.home.text-week': 'weeks',
  'page.home.text-month': 'month',
  'page.home.text-years': 'years',
  'page.home.text-total': 'total',
  'page.home.text-daycompare': 'day Year-on-year',
  'page.home.text-weekcompare': 'week Year-on-year',
  'page.home.articlechartcard.card-title': 'essays',
  'page.home.workschartcard.card-title': 'works',
  'page.home.topicschartcard.card-title': 'topics',
  'page.home.linkschartcard.card-title': 'neighbors',
  'page.home.hotsearchcard.card-title': 'top search',
  'page.home.hotsearchcard.card.table-column-number': 'serial number',
  'page.home.hotsearchcard.card.table-column-name': 'keywords',
  'page.home.hotsearchcard.card.table-column-hit': 'hits',
  'page.home.hottagscard.card-title': 'hot labels',
  'page.home.hottagscard.card.table-column-number': 'serial number',
  'page.home.hottagscard.card.table-column-name': 'label',
  'page.home.hottagscard.card.table-column-hit': 'hits',
  'page.home.articlehitcard.card-title': 'essay traffic ranking',
  'page.home.articlehitcard.card.table-column-number': 'serial number',
  'page.home.articlehitcard.card.table-column-title': 'title',
  'page.home.articlehitcard.card.table-column-hit': 'hits',
  'page.home.workshitcard.card-title': 'works traffic ranking',
  'page.home.workshitcard.card.table-column-number': 'serial number',
  'page.home.workshitcard.card.table-column-title': 'title',
  'page.home.workshitcard.card.table-column-hit': 'hits',

  'universal-layout.topmenu.userinfo': 'personal info',
  'universal-layout.topmenu.logout': 'logout',
  'universal-layout.menu.home': 'home',
  'universal-layout.menu.home.workplace': 'workplace',
  'universal-layout.menu.home.custom-breadcrumbs.montoacl.cc': 'montoacl.cc',
  'universal-layout.menu.home.bookmark': 'Colorful Bookmark',
  'universal-layout.menu.home.timestamp': 'Timesync',

  'universal-layout.menu.roles': 'permissions',
  'universal-layout.menu.roles.user': 'user list',
  'universal-layout.menu.roles.role': 'role list',
  'universal-layout.menu.roles.resource': 'resource list',

  'universal-layout.menu.aicodecheck': 'AI code check',
  'universal-layout.menu.aicodecheck.commonrule': 'common rule',
  'universal-layout.menu.aicodecheck.customrule': 'custom rule',
  'universal-layout.menu.aicodecheck.commentlist': 'comment list',
  'universal-layout.menu.aicodecheck.AIModel': 'AI Model',
  'universal-layout.menu.aicodecheck.GitlabToken': 'Gitlab Token',


  'page.resource.name': 'resource name',
  'page.resource.key': 'resource',
  'page.resource.category': 'category',
  'page.resource.describe': 'describe',
  'page.resource.add': 'create resource',

  'page.role.name': 'role name',
  'page.role.key': 'role',
  'page.role.describe': 'describe',
  'page.role.configresource': 'config resource',
  'page.role.add': 'create role',

  'page.user.enname': 'English name',
  'page.user.cnname': 'Chinese name',
  'page.user.password': 'password',
  'page.user.role': 'role',
  'page.user.job': 'job',
  'page.user.email': 'email',
  'page.user.phone': 'phone',
  'page.user.add': 'create user',
  'page.user.view': 'view user',

  'page.aicodecheck.rule.name': 'rule name',
  'page.aicodecheck.rule.project_id': 'project ID',
  'page.aicodecheck.rule.project_name': 'project name',
  'page.aicodecheck.rule.language': 'language',
  'page.aicodecheck.rule': 'rule',
  'page.aicodecheck.rule.id': 'rule ID',
  'page.aicodecheck.rule.description': 'rule desc',
  'page.aicodecheck.rule.add': 'create rule',
  'page.aicodecheck.comment.aimodel': 'AI model',
  'page.aicodecheck.comment.result': 'result',
  'page.aicodecheck.comment.createtime': 'create time',
  'page.aicodecheck.comment.status': 'status',
  'page.aicodecheck.comment.status.pass': 'passed',
  'page.aicodecheck.comment.status.fail': 'Failed',


  'page.aicodecheck.aimodel.name': 'model name',
  'page.aicodecheck.aimodel.model': 'model',
  'page.aicodecheck.aimodel.api_url': 'api url',
  'page.aicodecheck.aimodel.api_key': 'api key',
  'page.aicodecheck.aimodel.add': 'add model',

  'page.aicodecheck.gitlab.add': 'Add TOKEN',
  'page.aicodecheck.gitlab.api': 'API',
  'page.aicodecheck.gitlab.token': 'Token',
  'page.aicodecheck.gitlab.expired': 'Expired',
  'page.aicodecheck.gitlab.webhook_name': 'Webhook Name',
  'page.aicodecheck.gitlab.webhook_url': 'Webhook URL',
  'page.aicodecheck.gitlab.source_branch': 'Source Branch',
  'page.aicodecheck.gitlab.target_branch': 'Target Branch',

  'page.aicodecheck.comment.human_rating': 'rating',
  'page.aicodecheck.comment.remark': 'remark',

  'setting.pagestyle': 'page styles',
  'setting.navigationmode': 'nav mode',
  'setting.headfixed': 'head fixed',
  'setting.navtabs': 'nav tabs',

  'test.menuacl': 'menu permission',
  'test.pageacl': 'page permission',
  'test.apiacl': 'API permission',
};
