# Common build stage
FROM node:18-alpine as common-build-stage

COPY . ./app

WORKDIR /app

RUN npm i -g vite && npm i -g pnpm

# 不再接收构建参数，不打包环境变量到镜像
RUN pnpm install && pnpm build

# Production stage
FROM nginx:1.19.1 as production-build-stage

COPY ./nginx.conf /etc/nginx/conf.d/default.conf

COPY --from=common-build-stage /app/dist /usr/share/nginx/html

# 创建配置目录
RUN mkdir -p /usr/share/nginx/html/config

# 复制启动脚本
COPY docker-entrypoint.sh /docker-entrypoint.sh
RUN chmod +x /docker-entrypoint.sh

EXPOSE 80

CMD ["/docker-entrypoint.sh"]