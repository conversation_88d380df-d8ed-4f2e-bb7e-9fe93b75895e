# Common build stage
FROM node:18-alpine as common-build-stage

COPY . ./app

WORKDIR /app

RUN npm i -g vite && npm i -g pnpm

RUN pnpm install && pnpm build

RUN ls -al

# EXPOSE 3000

FROM nginx:1.19.1 as production-build-stage

COPY ./nginx.conf /etc/nginx/conf.d/default.conf

COPY --from=common-build-stage /app/dist /usr/share/nginx/html

# 创建配置目录和启动脚本
RUN mkdir -p /usr/share/nginx/html/config

# 创建启动脚本来动态生成配置文件
RUN echo '#!/bin/sh' > /docker-entrypoint.sh && \
    echo '# 生成运行时配置文件' >> /docker-entrypoint.sh && \
    echo 'cat > /usr/share/nginx/html/config/env.js << EOL' >> /docker-entrypoint.sh && \
    echo 'window.ENV = {' >> /docker-entrypoint.sh && \
    echo '  VITE_APP_APIHOST: '\''${VITE_APP_APIHOST}'\''' >> /docker-entrypoint.sh && \
    echo '};' >> /docker-entrypoint.sh && \
    echo 'EOL' >> /docker-entrypoint.sh && \
    echo '' >> /docker-entrypoint.sh && \
    echo '# 启动 nginx' >> /docker-entrypoint.sh && \
    echo 'exec nginx -g "daemon off;"' >> /docker-entrypoint.sh

RUN chmod +x /docker-entrypoint.sh

EXPOSE 80

CMD ["/docker-entrypoint.sh"]