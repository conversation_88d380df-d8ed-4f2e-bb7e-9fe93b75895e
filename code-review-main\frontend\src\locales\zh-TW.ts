export default {
  'app.empty': '空空如也',
  'app.global.menu.notfound': '沒有數據',
  'app.global.form.validatefields.catch': '驗證不通過，請檢查輸入',
  'app.global.tip.delete.success': '刪除成功',
  'app.global.tip.update.success': '更新成功',
  'app.global.tip.create.success': '創建成功',
  'app.global.close': '關閉',
  'app.global.delete': '刪除',
  'app.global.edit': '編輯',
  'app.global.view': '查看',
  'app.global.delete.tip': '確認刪除此條信息嗎',
  'app.global.doing': '開發中',

  'app.form.required': '必填',
  'app.form.search': '搜索',
  'app.form.reset': '重置',
  'app.form.expand': '展開',
  'app.form.refresh': '刷新',

  'app.table.large': '寬鬆',
  'app.table.middle': '中等',
  'app.table.small': '緊湊',
  'app.table.lineheight': '行高',
  'app.table.fuzzysearch': '模糊搜索',
  'app.table.operator': '更新人',
  'app.table.updatetime': '更新時間',
  'app.table.action': '操作',

  'user-layout.menu.login': '登錄',
  'user-layout.menu.register': '註冊',

  'page.user.login.form-item-username': '用戶名: admin',
  'page.user.login.form-item-username.required': '請輸入用戶名',
  'page.user.login.form-item-password': '密碼: 12345678',
  'page.user.login.form-item-password.required': '請輸入密碼',
  'page.user.login.form.title': '賬戶登錄',
  'page.user.login.form.btn-submit': '登錄',
  'page.user.login.form.btn-jump': '還沒有賬戶？現在註冊！',
  'page.user.login.form.login-error': '用戶名或密碼錯誤！',
  'page.user.login.form.login-success': '登錄成功！',

  'page.user.register.form-item-username': '用戶名',
  'page.user.register.form-item-username.required': '請輸入用戶名',
  'page.user.register.form-item-password': '密碼',
  'page.user.register.form-item-password.required': '請輸入密碼',
  'page.user.register.form-item-confirmpassword': '確認密碼',
  'page.user.register.form-item-confirmpassword.compare': '您輸入的兩個密碼不匹配！',
  'page.user.register.form.title': '註冊賬戶',
  'page.user.register.form.btn-submit': '註冊',
  'page.user.register.form.btn-jump': '已有賬戶？現在登錄！',
  'page.user.register.form.register-success': '註冊成功，請登錄！',

  'page.home.text-day': '日',
  'page.home.text-week': '周',
  'page.home.text-month': '月',
  'page.home.text-years': '年',
  'page.home.text-total': '總數量',
  'page.home.text-daycompare': '日同比',
  'page.home.text-weekcompare': '周同比',
  'page.home.articlechartcard.card-title': '隨筆',
  'page.home.workschartcard.card-title': '作品',
  'page.home.topicschartcard.card-title': '專題',
  'page.home.linkschartcard.card-title': '左鄰右舍',
  'page.home.hotsearchcard.card-title': '熱門搜索',
  'page.home.hotsearchcard.card.table-column-number': '序號',
  'page.home.hotsearchcard.card.table-column-name': '關鍵詞',
  'page.home.hotsearchcard.card.table-column-hit': '次數',
  'page.home.hottagscard.card-title': '熱門標簽',
  'page.home.hottagscard.card.table-column-number': '序號',
  'page.home.hottagscard.card.table-column-name': '標簽',
  'page.home.hottagscard.card.table-column-hit': '次數',
  'page.home.articlehitcard.card-title': '隨筆瀏覽量排行',
  'page.home.articlehitcard.card.table-column-number': '序號',
  'page.home.articlehitcard.card.table-column-title': '標題',
  'page.home.articlehitcard.card.table-column-hit': '瀏覽量',
  'page.home.workshitcard.card-title': '作品瀏覽量排行',
  'page.home.workshitcard.card.table-column-number': '序號',
  'page.home.workshitcard.card.table-column-title': '標題',
  'page.home.workshitcard.card.table-column-hit': '瀏覽量',

  'universal-layout.topmenu.userinfo': '個人信息',
  'universal-layout.topmenu.logout': '退出',
  'universal-layout.menu.home': '首頁',
  'universal-layout.menu.home.workplace': '工作臺',
  'universal-layout.menu.home.custom-breadcrumbs.montoacl.cc': 'montoacl.cc',
  'universal-layout.menu.home.bookmark': '多彩書簽',
  'universal-layout.menu.home.timestamp': '時間戳轉換',

  'universal-layout.menu.roles': '權限驗證',
  'universal-layout.menu.roles.user': '用戶列表',
  'universal-layout.menu.roles.role': '角色列表',
  'universal-layout.menu.roles.resource': '資源列表',

  'universal-layout.menu.aicodecheck': 'AI程式碼檢測',
  'universal-layout.menu.aicodecheck.commonrule': '通用規則',
  'universal-layout.menu.aicodecheck.customrule': '自定義規則',
  'universal-layout.menu.aicodecheck.commentlist': '評論列表',
  'universal-layout.menu.aicodecheck.AIModel': 'AI模型管理',
  'universal-layout.menu.aicodecheck.GitlabToken': 'Gitlab Token',

  'page.resource.name': '資源名稱',
  'page.resource.key': '資源標識',
  'page.resource.category': '資源類別',
  'page.resource.describe': '描述',
  'page.resource.add': '創建資源',

  'page.role.name': '角色名稱',
  'page.role.key': '角色標識',
  'page.role.describe': '描述',
  'page.role.configresource': '配置資源',
  'page.role.add': '創建角色',

  'page.user.enname': '英文名',
  'page.user.cnname': '中文名',
  'page.user.password': '密碼',
  'page.user.role': '角色',
  'page.user.job': '職位',
  'page.user.email': '郵箱',
  'page.user.phone': '手機號',
  'page.user.add': '創建用戶',
  'page.user.view': '查看用戶',

  'page.aicodecheck.rule.name': '規則名稱',
  'page.aicodecheck.rule.project_id': '規則隸屬項目ID',
  'page.aicodecheck.rule.project_name': '規則隸屬項目名称',
  'page.aicodecheck.rule.language': '語言',
  'page.aicodecheck.rule': '規則',
  'page.aicodecheck.rule.id': '規則 ID',
  'page.aicodecheck.rule.description': '規則描述',
  'page.aicodecheck.rule.add': '創建規則',
  'page.aicodecheck.comment.aimodel': 'AI 模型',
  'page.aicodecheck.comment.result': '審核結果',
  'page.aicodecheck.comment.createtime': '審核時間',
  'page.aicodecheck.comment.status': '審核狀態',
  'page.aicodecheck.comment.status.pass': '通過',
  'page.aicodecheck.comment.status.fail': '不通過',

  'page.aicodecheck.aimodel.name': '模型名称',
  'page.aicodecheck.aimodel.model': '模型',
  'page.aicodecheck.aimodel.api_url': 'API地址',
  'page.aicodecheck.aimodel.api_key': 'API密钥',
  'page.aicodecheck.aimodel.add': '添加模型',

  'page.aicodecheck.gitlab.add': '添加TOKEN',
  'page.aicodecheck.gitlab.api': 'API',
  'page.aicodecheck.gitlab.token': 'Token',
  'page.aicodecheck.gitlab.expired': '有效期',
  'page.aicodecheck.gitlab.webhook_name': 'webhook名稱',
  'page.aicodecheck.gitlab.webhook_url': 'webhook地址',
  'page.aicodecheck.gitlab.source_branch': '源分支',
  'page.aicodecheck.gitlab.target_branch': '目標分支',
  'page.aicodecheck.comment.human_rating': '評分',
  'page.aicodecheck.comment.remark': '備註',

  'setting.pagestyle': '頁面風格',
  'setting.navigationmode': '導航風格',
  'setting.headfixed': '固定頭部',
  'setting.navtabs': 'tab導航',

  'test.menuacl': '測試菜單權限',
  'test.pageacl': '測試頁面權限',
  'test.apiacl': '測試API權限',
};
