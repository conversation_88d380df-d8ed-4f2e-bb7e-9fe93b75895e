// 运行时环境变量工具函数
declare global {
  interface Window {
    ENV?: {
      VITE_APP_APIHOST?: string;
    };
  }
}

/**
 * 获取运行时环境变量
 * 优先使用运行时配置，如果没有则回退到构建时环境变量
 */
export function getEnv(key: string): string {
  // 首先尝试从运行时配置获取
  if (window.ENV && window.ENV[key as keyof typeof window.ENV]) {
    return window.ENV[key as keyof typeof window.ENV] as string;
  }
  
  // 回退到构建时环境变量
  return import.meta.env[key] || '';
}

/**
 * 获取 API 主机地址
 */
export function getApiHost(): string {
  return getEnv('VITE_APP_APIHOST');
}
