// 运行时环境变量工具函数

declare global {
  interface Window {
    ENV?: {
      VITE_APP_APIHOST?: string;
    };
  }
}

/**
 * 获取 API 主机地址
 * 优先使用运行时配置，如果没有则回退到构建时环境变量
 */
export function getApiHost(): string {
  // 首先尝试从运行时配置获取
  if (typeof window !== 'undefined' && window.ENV && window.ENV.VITE_APP_APIHOST) {
    return window.ENV.VITE_APP_APIHOST;
  }

  // 回退到构建时环境变量
  return import.meta.env.VITE_APP_APIHOST || '';
}
